package com.pacto.adm.core.controller;

import com.pacto.adm.core.dto.AvisoInternoDTO;
import com.pacto.adm.core.dto.enveloperesposta.acesso.perfilacesso.EnvelopeRespostaListPerfilAcessoDTO;
import com.pacto.adm.core.dto.enveloperesposta.avisointerno.EnvelopeRespostaAvisoInativado;
import com.pacto.adm.core.dto.enveloperesposta.avisointerno.EnvelopeRespostaAvisoInternoDTO;
import com.pacto.adm.core.dto.enveloperesposta.avisointerno.EnvelopeRespostaListAvisoInternoDTO;
import com.pacto.adm.core.dto.enveloperesposta.usuario.EnvelopeRespostaListUsuarioDTO;
import com.pacto.adm.core.services.interfaces.AvisoInternoService;
import com.pacto.config.exceptions.ServiceException;
import com.pacto.config.utils.EnvelopeRespostaDTO;
import com.pacto.config.utils.ResponseEntityFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.ExampleObject;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/aviso-interno")
public class AvisoInternoController {

    @Autowired
    private AvisoInternoService avisoInternoService;

    @Operation(
            summary = "Cadastrar ou atualizar aviso interno",
            description = "Cadastra ou atualiza um aviso interno. <br/>" +
                    "Para cadastrar é necessário retirar do corpo da requisição o ID identificador do aviso interno.<br/>" +
                    "Para atualizar é necessário colocar no corpo da requisição o código identificador do aviso interno.",
            tags = {"Aviso Interno"},
            parameters = {@Parameter(name = "empresa", description = "Código único identificador da empresa que vai cadastrar o Aviso Interno", example = "1")},
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    required = true,
                    description = "Informações para cadastro ou atualização do Aviso Interno",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = AvisoInternoDTO.class),
                            examples = {@ExampleObject(name = "Exemplo Request Body", value = EnvelopeRespostaAvisoInternoDTO.requestBody)}
                    )
            ),
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAvisoInternoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAvisoInternoDTO.resposta)}
                            )),
            }
    )
    @PostMapping("/{empresa}")
    public ResponseEntity<EnvelopeRespostaDTO> saveOrUpdate(@PathVariable("empresa") Integer empresa, @RequestBody AvisoInternoDTO avisoInternoDTO) {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.saveOrUpdateAvisoInterno(empresa, avisoInternoDTO));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Inativar aviso interno",
            description = "Inativa um aviso interno pelo código dele",
            tags = {"Aviso Interno"},
            parameters = {@Parameter(name = "codigo", description = "Código do aviso interno que será inativado", example = "434")},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAvisoInativado.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAvisoInativado.resposta)}
                            )),
            }
    )
    @PostMapping("/inativar-aviso-interno/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> inativarAvisoInterno(@PathVariable("codigo") Integer codigo) {
        try {
            avisoInternoService.inativarAvisoInterno(codigo);
            return ResponseEntityFactory.ok("Aviso inativado com sucesso!");
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }

    @Operation(
            summary = "Excluir um aviso interno",
            description = "Exclui um aviso interno pelo código dele",
            tags = {"Aviso Interno"},
            parameters = {@Parameter(name = "codigo", description = "Código do aviso interno que será excluído", example = "234")},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida"
                    )
            }
    )
    @DeleteMapping("/{codigo}")
    public ResponseEntity<EnvelopeRespostaDTO> excluirAvisoInterno(@PathVariable Integer codigo) {
        try {
            avisoInternoService.excluirAvisoInterno(codigo);
            return ResponseEntityFactory.ok();
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            StringBuilder result = new StringBuilder(e.toString() + "/n");
            StackTraceElement[] trace = e.getStackTrace();
            for (StackTraceElement stackTraceElement : trace) {
                result.append(stackTraceElement.toString()).append("/n");
            }
            return ResponseEntityFactory.erroInterno(e.getMessage(), result.toString());
        }
    }


    @Operation(
            summary = "Consultar os avisos internos que estão visíveis para todos",
            description = "Lista os avisos internos que estão visíveis para todos",
            tags = {"Aviso Interno"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAvisoInternoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAvisoInternoDTO.resposta)}
                            )),
            }
    )
    @GetMapping("/listar-avisos/visivel-todos")
    public ResponseEntity<EnvelopeRespostaDTO> listarAvisosVisiveisParaTodos() {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.listarAvisosParaTodos());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar todos os avisos internos do usuário logado no sistema",
            description = "Lista todos os avisos internos de um usuário que está logado no sistema, filtrando pela empresa",
            tags = {"Aviso Interno"},
            parameters = {
                    @Parameter(name = "empresa", description = "Código da empresa que publicou os avisos", example = "2", required = true)
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListAvisoInternoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListAvisoInternoDTO.resposta)}
                            )),
            }
    )
    @GetMapping("/listar-avisos/by-usuario/{empresa}")
    public ResponseEntity<EnvelopeRespostaDTO> listarAvisosByUsuario(@PathVariable Integer empresa) {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.listarAvisosByUsuario(empresa));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar um aviso interno",
            description = "Lista um aviso interno buscando pelo código dele",
            tags = {"Aviso Interno"},
            parameters = {
                    @Parameter(name = "codAviso", description = "Código do aviso que será consultado", example = "234")
            },
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaAvisoInternoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaAvisoInternoDTO.resposta)}
                            ))
            }
    )
    @GetMapping("/listar-avisos/{codAviso}")
    public ResponseEntity<EnvelopeRespostaDTO> listarAviso(@PathVariable Integer codAviso) {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.listarAviso(codAviso));
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


    @Operation(
            summary = "Consultar todos os colaboradores",
            description = "Lista todos os colaboradores cadastrados",
            tags = {"Colaborador"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListUsuarioDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListUsuarioDTO.resposta)}
                            )),

            }
    )
    @GetMapping("/listar-colaboradores")
    public ResponseEntity<EnvelopeRespostaDTO> listarColaboradores() {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.listarUsuarios());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }

    @Operation(
            summary = "Consultar todos perfis de acesso",
            description = "Lista todos os perfis de acesso",
            tags = {"Perfil de Acesso"},
            responses = {
                    @ApiResponse(
                            responseCode = "200",
                            description = "Solicitação bem-sucedida",
                            content = @Content(
                                    mediaType = "application/json",
                                    schema = @Schema(implementation = EnvelopeRespostaListPerfilAcessoDTO.class),
                                    examples = {@ExampleObject(name = "Exemplo Resposta 200", value = EnvelopeRespostaListPerfilAcessoDTO.resposta)}
                            )),
            }
    )
    @GetMapping("/listar-perfis-acesso")
    public ResponseEntity<EnvelopeRespostaDTO> listarPerfisAcesso() {
        try {
            return ResponseEntityFactory.ok(avisoInternoService.listarPerfisAcesso());
        } catch (ServiceException e) {
            return ResponseEntityFactory.mensagemFront(e.getChaveExcecao(), e.getMessage());
        }
    }


}
